package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"scs-camera/internal/models"
	"scs-camera/internal/repositories"
	"scs-camera/internal/services"
	my_webrtc "scs-camera/internal/webRTC"

	"github.com/google/uuid"
	"github.com/pion/webrtc/v4"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Example demonstrating camera activation when publisher starts
func main() {
	fmt.Println("=== Camera Activation Test ===\n")

	// Setup in-memory database for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Auto-migrate the camera model
	if err := db.AutoMigrate(&models.Camera{}); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// Create repositories and services
	cameraRepo := repositories.NewCameraRepository(db)
	premiseRepo := repositories.NewPremiseRepository(db)
	cameraService := services.NewCameraService(*cameraRepo, *premiseRepo)

	// Create stream manager
	streamManager, err := my_webrtc.NewStreamManager([]webrtc.ICEServer{
		{URLs: []string{"stun:stun.l.google.com:19302"}},
	})
	if err != nil {
		log.Fatal("Failed to create stream manager:", err)
	}

	// Set up camera activation callback
	streamManager.SetCameraActivationCallback(func(cameraID string, isActive bool) error {
		ctx := context.Background()
		if isActive {
			fmt.Printf("📹 Activating camera: %s\n", cameraID)
			return cameraService.ActivateCamera(ctx, cameraID)
		} else {
			fmt.Printf("📴 Deactivating camera: %s\n", cameraID)
			return cameraService.DeactivateCamera(ctx, cameraID)
		}
	})

	// Test scenario
	testCameraActivation(db, cameraService, streamManager)
}

func testCameraActivation(db *gorm.DB, cameraService *services.CameraService, streamManager *my_webrtc.StreamManager) {
	ctx := context.Background()

	// Step 1: Create a test camera
	fmt.Println("1. Creating test camera...")
	camera := &models.Camera{
		Base: models.Base{
			ID: uuid.New(),
		},
		Name:                "Test Camera 001",
		LocationDescription: "Test Location",
		IsActive:            false,
	}

	if err := db.Create(camera).Error; err != nil {
		log.Fatal("Failed to create camera:", err)
	}

	cameraID := camera.ID.String()
	fmt.Printf("   Created camera with ID: %s\n", cameraID)

	// Step 2: Check initial camera status
	fmt.Println("\n2. Checking initial camera status...")
	initialCamera, err := cameraService.GetCameraByID(ctx, cameraID)
	if err != nil {
		log.Fatal("Failed to get camera:", err)
	}
	fmt.Printf("   Camera active status: %v\n", initialCamera.IsActive)

	// Step 3: Simulate publisher starting (this should activate the camera)
	fmt.Println("\n3. Simulating publisher start...")
	
	// Create a mock offer (in real scenario, this comes from the client)
	offer := webrtc.SessionDescription{
		Type: webrtc.SDPTypeOffer,
		SDP:  "v=0\r\no=- 0 0 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\nm=video 9 UDP/TLS/RTP/SAVPF 96\r\nc=IN IP4 127.0.0.1\r\na=rtcp:9 IN IP4 127.0.0.1\r\na=ice-ufrag:test\r\na=ice-pwd:test\r\na=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00\r\na=setup:actpass\r\na=mid:0\r\na=sendrecv\r\na=rtcp-mux\r\na=rtpmap:96 VP8/90000\r\n",
	}

	// Mock send candidate function
	sendCandidate := func(cp my_webrtc.CandidatePayload) error {
		fmt.Printf("   ICE Candidate: %s\n", cp.Candidate)
		return nil
	}

	// Start publisher (this should trigger camera activation)
	answer, err := streamManager.StartPublisher(ctx, cameraID, offer, sendCandidate)
	if err != nil {
		fmt.Printf("   Publisher start failed (expected): %v\n", err)
		// This is expected to fail in test environment due to mock SDP
	} else {
		fmt.Printf("   Publisher started successfully, answer SDP length: %d\n", len(answer.SDP))
	}

	// Give some time for the activation callback to execute
	time.Sleep(100 * time.Millisecond)

	// Step 4: Check camera status after publisher start
	fmt.Println("\n4. Checking camera status after publisher start...")
	updatedCamera, err := cameraService.GetCameraByID(ctx, cameraID)
	if err != nil {
		log.Fatal("Failed to get updated camera:", err)
	}
	fmt.Printf("   Camera active status: %v\n", updatedCamera.IsActive)

	if updatedCamera.IsActive {
		fmt.Println("   ✅ Camera was successfully activated!")
	} else {
		fmt.Println("   ❌ Camera was not activated")
	}

	// Step 5: Simulate publisher stopping (remove broadcaster)
	fmt.Println("\n5. Simulating publisher stop...")
	streamManager.RemoveBroadcaster(cameraID)

	// Give some time for the deactivation callback to execute
	time.Sleep(100 * time.Millisecond)

	// Step 6: Check camera status after publisher stop
	fmt.Println("\n6. Checking camera status after publisher stop...")
	finalCamera, err := cameraService.GetCameraByID(ctx, cameraID)
	if err != nil {
		log.Fatal("Failed to get final camera:", err)
	}
	fmt.Printf("   Camera active status: %v\n", finalCamera.IsActive)

	if !finalCamera.IsActive {
		fmt.Println("   ✅ Camera was successfully deactivated!")
	} else {
		fmt.Println("   ❌ Camera was not deactivated")
	}

	fmt.Println("\n=== Test completed ===")
}

// Example of how to manually activate/deactivate cameras
func manualCameraControl() {
	// This shows how you can manually control camera status
	// outside of the publisher lifecycle

	// Setup (same as above)
	db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	db.AutoMigrate(&models.Camera{})
	
	cameraRepo := repositories.NewCameraRepository(db)
	premiseRepo := repositories.NewPremiseRepository(db)
	cameraService := services.NewCameraService(*cameraRepo, *premiseRepo)

	ctx := context.Background()
	cameraID := "test-camera-id"

	// Manual activation
	fmt.Println("Manually activating camera...")
	if err := cameraService.ActivateCamera(ctx, cameraID); err != nil {
		fmt.Printf("Failed to activate camera: %v\n", err)
	}

	// Manual deactivation
	fmt.Println("Manually deactivating camera...")
	if err := cameraService.DeactivateCamera(ctx, cameraID); err != nil {
		fmt.Printf("Failed to deactivate camera: %v\n", err)
	}
}
