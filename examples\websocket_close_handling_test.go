package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"strings"
	"time"
	ws "scs-camera/internal/websocket"

	"github.com/gorilla/websocket"
)

// Test to verify WebSocket close error handling improvements
func main() {
	fmt.Println("=== WebSocket Close Error Handling Test ===\n")

	// Test 1: Normal connection and graceful close
	fmt.Println("1. Testing normal connection and graceful close...")
	testNormalClose()

	// Test 2: Abrupt disconnection (simulates close code 1005)
	fmt.Println("\n2. Testing abrupt disconnection...")
	testAbruptDisconnection()

	// Test 3: Ping/Pong functionality
	fmt.Println("\n3. Testing ping/pong functionality...")
	testPingPong()

	// Test 4: Connection timeout handling
	fmt.Println("\n4. Testing connection timeout handling...")
	testConnectionTimeout()

	fmt.Println("\n=== All close handling tests completed! ===")
}

// Test normal connection and graceful close
func testNormalClose() {
	server := createTestServer()
	defer server.Close()

	// Connect to server
	url := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("Dial error:", err)
	}

	// Send a test message
	testMsg := map[string]interface{}{
		"type":    "test",
		"message": "Hello server",
	}
	
	if err := conn.WriteJSON(testMsg); err != nil {
		log.Printf("Write error: %v", err)
		return
	}

	// Read response
	var response map[string]interface{}
	if err := conn.ReadJSON(&response); err != nil {
		log.Printf("Read error: %v", err)
		return
	}

	fmt.Printf("   ✅ Received response: %v\n", response)

	// Close gracefully
	err = conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, "Test complete"))
	if err != nil {
		log.Printf("Close write error: %v", err)
	}

	conn.Close()
	fmt.Println("   ✅ Connection closed gracefully")
}

// Test abrupt disconnection (simulates what causes close code 1005)
func testAbruptDisconnection() {
	server := createTestServer()
	defer server.Close()

	// Connect to server
	url := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("Dial error:", err)
	}

	// Send a test message
	testMsg := map[string]interface{}{
		"type":    "test",
		"message": "About to disconnect abruptly",
	}
	
	if err := conn.WriteJSON(testMsg); err != nil {
		log.Printf("Write error: %v", err)
		return
	}

	// Simulate abrupt disconnection (like browser tab close)
	// This will cause the server to see a close code 1005
	conn.Close() // Abrupt close without proper close handshake

	fmt.Println("   ✅ Simulated abrupt disconnection (should generate close code 1005)")
	
	// Give server time to process the disconnection
	time.Sleep(100 * time.Millisecond)
}

// Test ping/pong functionality
func testPingPong() {
	server := createTestServer()
	defer server.Close()

	// Connect to server
	url := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("Dial error:", err)
	}
	defer conn.Close()

	// Set up pong handler
	pongReceived := make(chan bool, 1)
	conn.SetPongHandler(func(appData string) error {
		fmt.Println("   📡 Received pong from server")
		pongReceived <- true
		return nil
	})

	// Send ping to server
	fmt.Println("   📡 Sending ping to server...")
	pingMsg := map[string]string{"type": "ping"}
	if err := conn.WriteJSON(pingMsg); err != nil {
		log.Printf("Ping write error: %v", err)
		return
	}

	// Wait for pong response
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Read messages in a goroutine
	go func() {
		for {
			var msg map[string]interface{}
			if err := conn.ReadJSON(&msg); err != nil {
				return
			}
			if msgType, ok := msg["type"].(string); ok && msgType == "pong" {
				fmt.Println("   📡 Received pong message from server")
				pongReceived <- true
				return
			}
		}
	}()

	select {
	case <-pongReceived:
		fmt.Println("   ✅ Ping/Pong test successful")
	case <-ctx.Done():
		fmt.Println("   ❌ Ping/Pong test timed out")
	}
}

// Test connection timeout handling
func testConnectionTimeout() {
	server := createTestServer()
	defer server.Close()

	// Connect to server
	url := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("Dial error:", err)
	}
	defer conn.Close()

	// Send initial message
	testMsg := map[string]interface{}{
		"type":    "test",
		"message": "Testing timeout handling",
	}
	
	if err := conn.WriteJSON(testMsg); err != nil {
		log.Printf("Write error: %v", err)
		return
	}

	fmt.Println("   ⏰ Waiting to test timeout handling...")
	
	// Wait and see if server sends ping
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	pingReceived := false
	go func() {
		for {
			var msg map[string]interface{}
			if err := conn.ReadJSON(&msg); err != nil {
				return
			}
			if msgType, ok := msg["type"].(string); ok && msgType == "ping" {
				fmt.Println("   📡 Received ping from server")
				pingReceived = true
				
				// Respond with pong
				pongMsg := map[string]string{"type": "pong"}
				conn.WriteJSON(pongMsg)
				fmt.Println("   📡 Sent pong response")
				return
			}
		}
	}()

	<-ctx.Done()
	
	if pingReceived {
		fmt.Println("   ✅ Server ping/pong system working")
	} else {
		fmt.Println("   ⚠️  No ping received from server (may need longer wait)")
	}
}

// Create a test WebSocket server
func createTestServer() *httptest.Server {
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool { return true },
	}

	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		conn, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			log.Printf("Upgrade error: %v", err)
			return
		}
		defer func() {
			conn.Close()
			fmt.Println("   🔌 Server connection closed")
		}()

		// Set up ping/pong handling
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		conn.SetPongHandler(func(string) error {
			conn.SetReadDeadline(time.Now().Add(60 * time.Second))
			fmt.Println("   📡 Server received pong")
			return nil
		})

		// Start ping routine
		go func() {
			ticker := time.NewTicker(5 * time.Second) // Shorter interval for testing
			defer ticker.Stop()
			for {
				select {
				case <-ticker.C:
					if err := conn.WriteJSON(map[string]string{"type": "ping"}); err != nil {
						return
					}
				}
			}
		}()

		// Handle messages
		for {
			var msg map[string]interface{}
			if err := conn.ReadJSON(&msg); err != nil {
				// Handle different close errors
				if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway, websocket.CloseNoStatusReceived) {
					fmt.Printf("   ℹ️  Connection closed normally: %v\n", err)
				} else if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					fmt.Printf("   ⚠️  Connection closed unexpectedly: %v\n", err)
				} else {
					fmt.Printf("   ❌ Read error: %v\n", err)
				}
				return
			}

			// Handle different message types
			if msgType, ok := msg["type"].(string); ok {
				switch msgType {
				case "ping":
					// Respond to ping with pong
					conn.WriteJSON(map[string]string{"type": "pong"})
				case "pong":
					// Client responded to our ping
					conn.SetReadDeadline(time.Now().Add(60 * time.Second))
				case "test":
					// Echo test message
					response := map[string]interface{}{
						"type":     "response",
						"echo":     msg,
						"received": time.Now().Format(time.RFC3339),
					}
					conn.WriteJSON(response)
				default:
					fmt.Printf("   ❓ Unknown message type: %s\n", msgType)
				}
			}
		}
	}))
}
