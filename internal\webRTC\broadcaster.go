package my_webrtc

import (
	"context"
	"errors"
	"log"
	"scs-camera/pkg/logger"
	"sync"

	"github.com/pion/webrtc/v4"
)

// viewer holds state for a viewer connection
type viewer struct {
	id             string
	pc             *webrtc.PeerConnection
	candidateQueue []webrtc.ICECandidateInit
	lock           sync.Mutex
	closed         bool
	cancel         context.CancelFunc
}

// Broadcaster relays a publisher track to many viewers.
type Broadcaster struct {
	cameraID string

	api    *webrtc.API
	config webrtc.Configuration

	// publisher pc & candidate queue
	pubLock        sync.Mutex
	publisherPC    *webrtc.PeerConnection
	publisherQueue []webrtc.ICECandidateInit

	// track to forward to viewers (TrackLocalStaticRTP)
	trackLock  sync.RWMutex
	localTrack *webrtc.TrackLocalStaticRTP

	// viewers
	viewerLock sync.RWMutex
	viewers    map[string]*viewer

	ctx    context.Context
	cancel context.CancelFunc
	logger *logger.ApiLogger
}

// NewBroadcaster creates a broadcaster bound to an API and ICE config
func NewBroadcaster(cameraID string, api *webrtc.API, cfg webrtc.Configuration) *Broadcaster {
	ctx, cancel := context.WithCancel(context.Background())
	return &Broadcaster{
		cameraID: cameraID,
		api:      api,
		config:   cfg,
		viewers:  make(map[string]*viewer),
		ctx:      ctx,
		cancel:   cancel,
		logger:   logger.GetLogger(),
	}
}

// Close tears down publisher, viewers and track
func (b *Broadcaster) Close() {
	// Stop background work
	b.cancel()

	// Close publisher
	b.pubLock.Lock()
	if b.publisherPC != nil {
		_ = b.publisherPC.Close()
		b.publisherPC = nil
	}
	b.publisherQueue = nil
	b.pubLock.Unlock()

	// Close viewers
	b.viewerLock.Lock()
	for id, v := range b.viewers {
		v.lock.Lock()
		if !v.closed {
			if v.cancel != nil {
				v.cancel()
			}
			_ = v.pc.Close()
			v.closed = true
		}
		v.lock.Unlock()
		delete(b.viewers, id)
	}
	b.viewerLock.Unlock()

	// Clear track
	b.trackLock.Lock()
	b.localTrack = nil
	b.trackLock.Unlock()
}

// --------------------------- Publisher related ---------------------------

// StartPublisher accepts the publisher offer, setup PC, and returns answer (SDP).
// sendCandidate is a callback that will be invoked when local ICE candidates appear,
// callback should marshal candidate and send to client via websocket.
func (b *Broadcaster) StartPublisher(ctx context.Context, offer webrtc.SessionDescription, streamManager *StreamManager, sendCandidate func(sp CandidatePayload) error) (webrtc.SessionDescription, error) {
	b.pubLock.Lock()
	defer b.pubLock.Unlock()

	// If publisher already exists - refuse or replace. Here we replace old one.
	if b.publisherPC != nil {
		_ = b.publisherPC.Close()
		b.publisherPC = nil
		b.publisherQueue = nil
	}

	pc, err := b.api.NewPeerConnection(b.config)
	if err != nil {
		return webrtc.SessionDescription{}, err
	}

	// Save early so incoming ICE candidate messages can be added
	b.publisherPC = pc

	// Process any queued candidates
	for _, queuedCand := range b.publisherQueue {
		if err := pc.AddICECandidate(queuedCand); err != nil {
			log.Printf("broadcaster[%s] failed to add queued candidate: %v", b.cameraID, err)
		}
	}
	b.publisherQueue = nil // clear the queue

	// Monitor connection state
	pc.OnConnectionStateChange(func(state webrtc.PeerConnectionState) {
		b.logger.Infof("broadcaster[%s] publisher connection state: %s", b.cameraID, state.String())
		if state == webrtc.PeerConnectionStateFailed || state == webrtc.PeerConnectionStateDisconnected || state == webrtc.PeerConnectionStateClosed {
			go func() {
				streamManager.RemoveBroadcaster(b.cameraID)
			}()
		}
	})

	// OnICECandidate -> send out as they arrive (trickle)
	pc.OnICECandidate(func(c *webrtc.ICECandidate) {
		if c == nil {
			return
		}
		payload := CandidatePayload{
			Candidate:     c.ToJSON().Candidate,
			SDPMid:        &c.SDPMid,
			SDPMLineIndex: &c.SDPMLineIndex,
		}
		// best-effort send; log error but do not crash
		if sendErr := sendCandidate(payload); sendErr != nil {
			b.logger.Errorf("broadcaster[%s] sendCandidate err: %v", b.cameraID, sendErr)
		}
	})

	// Add transceiver to accept video
	if _, err = pc.AddTransceiverFromKind(webrtc.RTPCodecTypeVideo); err != nil {
		_ = pc.Close()
		return webrtc.SessionDescription{}, err
	}

	// OnTrack - create a local track and start reading RTP packets to forward to viewers
	pc.OnTrack(func(remoteTrack *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
		b.logger.Infof("broadcaster %s: publisher onTrack: codec=%s\n", b.cameraID, remoteTrack.Codec().MimeType)

		lt, err := webrtc.NewTrackLocalStaticRTP(remoteTrack.Codec().RTPCodecCapability, "video", b.cameraID)
		if err != nil {
			b.logger.Errorf("broadcaster %s: failed create local track: %v", b.cameraID, err)
			return
		}

		// set as current track
		b.trackLock.Lock()
		b.localTrack = lt
		b.trackLock.Unlock()

		// copy loop
		buf := make([]byte, 1400)
		for {
			n, _, readErr := remoteTrack.Read(buf)
			if readErr != nil {
				// End of track or error
				b.logger.Errorf("broadcaster %s: remoteTrack.Read error: %v", b.cameraID, readErr)
				b.trackLock.Lock()
				b.localTrack = nil
				b.trackLock.Unlock()
				return
			}
			// Attempt to write to local track; ignore ErrClosedPipe (no viewers).
			b.trackLock.RLock()
			if b.localTrack != nil {
				if _, wErr := b.localTrack.Write(buf[:n]); wErr != nil {
					// ErrClosedPipe is expected when no viewers are connected
					if wErr.Error() != "write on closed pipe" {
						b.logger.Errorf("broadcaster %s: localTrack.Write error: %v", b.cameraID, wErr)
					}
				}
			}
			b.trackLock.RUnlock()
		}
	})

	// set remote description and create answer (trickle candidates will stream via OnICECandidate)
	if err := pc.SetRemoteDescription(offer); err != nil {
		_ = pc.Close()
		b.publisherPC = nil
		return webrtc.SessionDescription{}, err
	}

	answer, err := pc.CreateAnswer(nil)
	if err != nil {
		_ = pc.Close()
		b.publisherPC = nil
		return webrtc.SessionDescription{}, err
	}

	if err := pc.SetLocalDescription(answer); err != nil {
		_ = pc.Close()
		b.publisherPC = nil
		return webrtc.SessionDescription{}, err
	}

	// NOTE: we don't block for GatheringCompletePromise because we are using trickle ICE.
	return *pc.LocalDescription(), nil
}

// AddPublisherICECandidate add candidate to publisher's PC (queues if PC not ready)
func (b *Broadcaster) AddPublisherICECandidate(cand webrtc.ICECandidateInit) error {
	b.pubLock.Lock()
	defer b.pubLock.Unlock()
	if b.publisherPC == nil {
		// queue
		b.publisherQueue = append(b.publisherQueue, cand)
		return nil
	}
	return b.publisherPC.AddICECandidate(cand)
}

// --------------------------- Viewer related ---------------------------

// AddViewer creates viewer PeerConnection, attaches the localTrack and returns answer SDP.
func (b *Broadcaster) AddViewer(parentCtx context.Context, viewerID string, offer webrtc.SessionDescription, sendCandidate func(sp CandidatePayload) error) (webrtc.SessionDescription, error) {
	// ensure there's a track
	b.trackLock.RLock()
	lt := b.localTrack
	b.trackLock.RUnlock()
	if lt == nil {
		return webrtc.SessionDescription{}, errors.New("publisher track not available")
	}

	// create a viewer struct with its own context
	_, cancel := context.WithCancel(parentCtx)
	v := &viewer{
		id:             viewerID,
		candidateQueue: nil,
		cancel:         cancel,
	}

	pc, err := b.api.NewPeerConnection(b.config)
	if err != nil {
		cancel()
		return webrtc.SessionDescription{}, err
	}
	v.pc = pc

	// OnICECandidate for viewer -> forward to client via sendCandidate
	pc.OnICECandidate(func(c *webrtc.ICECandidate) {
		if c == nil {
			return
		}
		payload := CandidatePayload{
			Candidate:     c.ToJSON().Candidate,
			SDPMid:        &c.SDPMid,
			SDPMLineIndex: &c.SDPMLineIndex,
		}
		if sendErr := sendCandidate(payload); sendErr != nil {
			b.logger.Errorf("broadcaster[%s][viewer=%s] sendCandidate err: %v", b.cameraID, viewerID, sendErr)
		}
	})
	pc.OnConnectionStateChange(func(state webrtc.PeerConnectionState) {
		b.logger.Infof("broadcaster[%s][viewer=%s] connection state: %s", b.cameraID, viewerID, state.String())
		if state == webrtc.PeerConnectionStateFailed || state == webrtc.PeerConnectionStateDisconnected || state == webrtc.PeerConnectionStateClosed {
			go func() {
				b.RemoveViewer(viewerID)
			}()
		}
	})

	// Add the shared local track
	if _, err := pc.AddTrack(lt); err != nil {
		_ = pc.Close()
		cancel()
		return webrtc.SessionDescription{}, err
	}

	// set remote desc & create answer
	if err := pc.SetRemoteDescription(offer); err != nil {
		_ = pc.Close()
		cancel()
		return webrtc.SessionDescription{}, err
	}

	answer, err := pc.CreateAnswer(nil)
	if err != nil {
		_ = pc.Close()
		cancel()
		return webrtc.SessionDescription{}, err
	}
	if err := pc.SetLocalDescription(answer); err != nil {
		_ = pc.Close()
		cancel()
		return webrtc.SessionDescription{}, err
	}

	// save the viewer
	b.viewerLock.Lock()
	b.viewers[viewerID] = v

	b.viewerLock.Unlock()
	return *pc.LocalDescription(), nil
}

// AddViewerICECandidate adds candidate to viewer's PC (if PC not ready, queue).
func (b *Broadcaster) AddViewerICECandidate(viewerID string, cand webrtc.ICECandidateInit) error {
	b.viewerLock.RLock()
	v, ok := b.viewers[viewerID]
	b.viewerLock.RUnlock()
	if !ok {
		// viewer not present; we could queue globally or return error
		return errors.New("viewer not found")
	}
	v.lock.Lock()
	defer v.lock.Unlock()
	if v.pc == nil {
		v.candidateQueue = append(v.candidateQueue, cand)
		return nil
	}
	return v.pc.AddICECandidate(cand)
}

func (b *Broadcaster) RemoveViewer(viewerID string) {
	b.viewerLock.RLock()
	v, ok := b.viewers[viewerID]
	b.viewerLock.RUnlock()
	if !ok {
		return
	}
	v.lock.Lock()
	if !v.closed {
		if v.cancel != nil {
			v.cancel()
		}
		_ = v.pc.Close()
		v.closed = true
	}
	v.lock.Unlock()
	b.viewerLock.Lock()
	delete(b.viewers, viewerID)
	b.viewerLock.Unlock()
}
