package ws

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log"
	"net/http"
	my_webrtc "scs-camera/internal/webRTC"
	"scs-camera/pkg/logger"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/pion/webrtc/v4"
)

const WriteTimeout = 5 * time.Second

// ConnectionManager manages WebSocket connections
type ConnectionManager struct {
	mu          sync.RWMutex
	connections map[string]*websocket.Conn // key: connectionID, value: websocket connection
}

var (
	once              sync.Once
	connectionManager *ConnectionManager
)

var upgrader = websocket.Upgrader{
	CheckOrigin:     func(r *http.Request) bool { return true },
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
}

// GetConnectionManager returns the singleton connection manager
func GetConnectionManager() *ConnectionManager {
	once.Do(func() {
		connectionManager = &ConnectionManager{
			connections: make(map[string]*websocket.Conn),
		}
	})
	return connectionManager
}

// AddConnection adds a new WebSocket connection to the manager
func (cm *ConnectionManager) AddConnection(connectionID string, conn *websocket.Conn) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.connections[connectionID] = conn
}

// RemoveConnection removes a WebSocket connection from the manager
func (cm *ConnectionManager) RemoveConnection(connectionID string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	if conn, exists := cm.connections[connectionID]; exists {
		conn.Close()
		delete(cm.connections, connectionID)
	}
}

// GetConnection retrieves a WebSocket connection by ID
func (cm *ConnectionManager) GetConnection(connectionID string) (*websocket.Conn, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	conn, exists := cm.connections[connectionID]
	return conn, exists
}

// BroadcastToAll sends a message to all connected clients
func (cm *ConnectionManager) BroadcastToAll(message interface{}) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	for connectionID, conn := range cm.connections {
		if err := writeJSONWithDeadline(conn, message); err != nil {
			log.Printf("Failed to send message to connection %s: %v", connectionID, err)
			// Remove failed connection
			go cm.RemoveConnection(connectionID)
		}
	}
}

// GetActiveConnections returns the number of active connections
func (cm *ConnectionManager) GetActiveConnections() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return len(cm.connections)
}

func writeJSONWithDeadline(ws *websocket.Conn, v interface{}) error {
	ws.SetWriteDeadline(time.Now().Add(WriteTimeout))
	return ws.WriteJSON(v)
}

// generateConnectionID creates a unique connection ID based on request info
func generateConnectionID(r *http.Request) string {
	// Generate random bytes for uniqueness
	randomBytes := make([]byte, 8)
	rand.Read(randomBytes)
	randomStr := hex.EncodeToString(randomBytes)

	// Include remote address for debugging purposes
	remoteAddr := r.RemoteAddr
	if remoteAddr == "" {
		remoteAddr = "unknown"
	}

	return fmt.Sprintf("%s_%s", remoteAddr, randomStr)
}
func WSHandler(sm *my_webrtc.StreamManager) http.HandlerFunc {
	logger := logger.GetLogger()
	return func(w http.ResponseWriter, r *http.Request) {
		ws, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			log.Printf("ws upgrade error: %v", err)
			return
		}

		// Generate unique connection ID
		connectionID := generateConnectionID(r)

		// Add connection to manager
		cm := GetConnectionManager()
		cm.AddConnection(connectionID, ws)

		defer func() {
			cm.RemoveConnection(connectionID)
			ws.Close()
		}()

		logger.Infof("New WebSocket connection established: %s (Total active: %d)", connectionID, cm.GetActiveConnections())

		// client identification (client can later send CameraID)
		ctx := r.Context()

		sendCandidateToClient := func(cam string, viewerID string) func(cp my_webrtc.CandidatePayload) error {
			return func(cp my_webrtc.CandidatePayload) error {
				msg := my_webrtc.SignalingMessage{
					Type:     "candidate",
					CameraID: cam,
					ViewerID: viewerID,
					Candidate: &my_webrtc.CandidatePayload{
						Candidate:     cp.Candidate,
						SDPMid:        cp.SDPMid,
						SDPMLineIndex: cp.SDPMLineIndex,
					},
				}
				return writeJSONWithDeadline(ws, msg)
			}
		}

		for {
			var in my_webrtc.SignalingMessage

			if err := ws.ReadJSON(&in); err != nil {
				logger.Errorf("ws read json err: %v", err)
				return
			}
			switch in.Type {
			case "offer":
				if in.Role == "publisher" && in.SDP != nil {
					// Build SessionDescription from incoming payload
					offer := webrtc.SessionDescription{
						Type: webrtc.SDPTypeOffer,
						SDP:  in.SDP.SDP,
					}
					// Start publisher (trickle candidate send uses sendCandidateToClient)
					answer, err := sm.StartPublisher(ctx, in.CameraID, offer, sendCandidateToClient(in.CameraID, in.ViewerID))
					if err != nil {
						log.Printf("StartPublisher err: %v", err)
						writeJSONWithDeadline(ws, my_webrtc.SignalingMessage{Type: "error", CameraID: in.CameraID, ViewerID: in.ViewerID})
						continue
					}
					// send answer
					resp := my_webrtc.SignalingMessage{
						Type:     "answer",
						CameraID: in.CameraID,
						ViewerID: in.ViewerID,
						SDP: &my_webrtc.SDPPayload{
							Type: "answer",
							SDP:  answer.SDP,
						},
					}
					_ = writeJSONWithDeadline(ws, resp)
				} else if in.Role == "viewer" && in.SDP != nil {
					offer := webrtc.SessionDescription{Type: webrtc.SDPTypeOffer, SDP: in.SDP.SDP}
					answer, err := sm.AddViewerToBroadcaster(ctx, in.CameraID, in.ViewerID, offer, sendCandidateToClient(in.CameraID, in.ViewerID))
					if err != nil {
						log.Printf("AddViewerToBroadcaster err: %v", err)
						_ = writeJSONWithDeadline(ws, my_webrtc.SignalingMessage{Type: "error", CameraID: in.CameraID, ViewerID: in.ViewerID})
						continue
					}
					resp := my_webrtc.SignalingMessage{
						Type:     "answer",
						CameraID: in.CameraID,
						ViewerID: in.ViewerID,
						SDP: &my_webrtc.SDPPayload{
							Type: "answer",
							SDP:  answer.SDP,
						},
					}
					_ = writeJSONWithDeadline(ws, resp)
				} else {
					_ = writeJSONWithDeadline(ws, my_webrtc.SignalingMessage{Type: "error", CameraID: in.CameraID, ViewerID: in.ViewerID})
				}
			case "candidate":
				// Candidate JSON includes CameraID and ClientID.
				if in.Candidate == nil {
					continue
				}
				cand := webrtc.ICECandidateInit{
					Candidate:     in.Candidate.Candidate,
					SDPMid:        in.Candidate.SDPMid,
					SDPMLineIndex: in.Candidate.SDPMLineIndex,
				}
				// Decide whether candidate is for publisher or viewer based on role or presence
				if in.Role == "publisher" {
					if err := sm.AddPublisherCandidate(in.CameraID, cand); err != nil {
						log.Printf("AddPublisherCandidate err: %v", err)
					}
				} else {
					if err := sm.AddViewerCandidate(in.CameraID, in.ViewerID, cand); err != nil {
						log.Printf("AddViewerCandidate err: %v", err)
					}
				}

			case "leave":
				// client leaves - if publisher removes broadcaster, if viewer remove viewer
				if in.Role == "publisher" {
					sm.RemoveBroadcaster(in.CameraID)
				} else {
					// remove viewer if present
					// For brevity: implement RemoveViewer in Broadcaster if needed.
					// b.RemoveViewer(in.ClientID)
				}
			}
		}
	}
}
