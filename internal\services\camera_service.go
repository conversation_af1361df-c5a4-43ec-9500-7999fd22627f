package services

import (
	"context"
	dto "scs-camera/internal/dto"
	"scs-camera/internal/models"
	repositories "scs-camera/internal/repositories"
	"scs-camera/internal/types"
	"scs-camera/pkg/errors"

	"github.com/google/uuid"
)

type CameraService struct {
	cameraRepo  repositories.CameraRepository
	premiseRepo repositories.PremiseRepository
}

func NewCameraService(cameraRepo repositories.CameraRepository, premiseRepo repositories.PremiseRepository) *CameraService {
	return &CameraService{
		cameraRepo:  cameraRepo,
		premiseRepo: premiseRepo,
	}
}

func (s *CameraService) CreateCamera(ctx context.Context, createCameraDto *dto.CreateCameraDto) (*models.Camera, error) {
	camera := &models.Camera{
		Name:                createCameraDto.Name,
		LocationDescription: createCameraDto.LocationDescription,
		IsActive:            false,
	}
	if createCameraDto.PremiseID != "" {
		premiseID, err := uuid.Parse(createCameraDto.PremiseID)

		if err != nil {
			return nil, err
		}

		premise, err := s.premiseRepo.GetPremiseByID(ctx, premiseID.String())

		if err != nil {
			return nil, err
		}
		camera.Premise = premise
		camera.PremiseID = premiseID

	}

	return s.cameraRepo.CreateCamera(ctx, camera)
}

func (s *CameraService) GetCameras(ctx context.Context, getCamerasRequest *dto.GetCamerasRequest) (*types.PaginateResponse[models.Camera], error) {
	cameras, err := s.cameraRepo.GetCameras(ctx, getCamerasRequest)

	if err != nil {
		return nil, errors.NewDatabaseError("get cameras", err)
	}
	total, err := s.cameraRepo.GetCamerasCount(ctx)
	totalPages := int(total) / getCamerasRequest.Limit
	if total%int64(getCamerasRequest.Limit) != 0 {
		totalPages++
	}

	if err != nil {
		return nil, errors.NewDatabaseError("get cameras count", err)
	}
	paginateResponse := types.PaginateResponse[models.Camera]{
		Pagination: types.Pagination{
			TotalPages: int(totalPages),
			Page:       getCamerasRequest.Page,
			Limit:      getCamerasRequest.Limit,
		},
		Data: cameras,
	}
	return &paginateResponse, nil
}

// ActivateCamera sets a camera as active when publisher starts successfully
func (s *CameraService) ActivateCamera(ctx context.Context, cameraID string) error {
	return s.cameraRepo.UpdateCameraStatus(ctx, cameraID, true)
}

// DeactivateCamera sets a camera as inactive when publisher stops
func (s *CameraService) DeactivateCamera(ctx context.Context, cameraID string) error {
	return s.cameraRepo.UpdateCameraStatus(ctx, cameraID, false)
}

// GetCameraByID retrieves a camera by its ID
func (s *CameraService) GetCameraByID(ctx context.Context, cameraID string) (*models.Camera, error) {
	return s.cameraRepo.GetCameraByID(ctx, cameraID)
}

func (s *CameraService) UpdateCamera(ctx context.Context, cameraID string, updateCameraDto *dto.UpdateCameraDto) (*models.Camera, error) {
	camera, err := s.cameraRepo.GetCameraByID(ctx, cameraID)
	if err != nil {
		return nil, err
	}
	camera.Name = updateCameraDto.Name
	camera.LocationDescription = updateCameraDto.LocationDescription
	if updateCameraDto.PremiseID != "" {
		premiseID, err := uuid.Parse(updateCameraDto.PremiseID)
		if err != nil {
			return nil, err
		}
		premise, err := s.premiseRepo.GetPremiseByID(ctx, premiseID.String())
		if err != nil {
			return nil, err
		}
		camera.Premise = premise
		camera.PremiseID = premiseID
	}
	return s.cameraRepo.UpdateCamera(ctx, camera)
}
