package services

import (
	"context"
	dto "scs-camera/internal/dto"
	"scs-camera/internal/models"
	repositories "scs-camera/internal/repositories"

	"github.com/google/uuid"
)

type CameraService struct {
	cameraRepo  repositories.CameraRepository
	premiseRepo repositories.PremiseRepository
}

func NewCameraService(cameraRepo repositories.CameraRepository, premiseRepo repositories.PremiseRepository) *CameraService {
	return &CameraService{
		cameraRepo:  cameraRepo,
		premiseRepo: premiseRepo,
	}
}

func (s *CameraService) CreateCamera(ctx context.Context, createCameraDto *dto.CreateCameraDto) (*models.Camera, error) {
	camera := &models.Camera{
		Name:                createCameraDto.Name,
		LocationDescription: createCameraDto.LocationDescription,
		IsActive:            false,
	}
	if createCameraDto.PremiseID != "" {
		premiseID, err := uuid.Parse(createCameraDto.PremiseID)

		if err != nil {
			return nil, err
		}

		premise, err := s.premiseRepo.GetPremiseByID(ctx, premiseID.String())

		if err != nil {
			return nil, err
		}
		camera.Premise = premise
		camera.PremiseID = premiseID

	}

	return s.cameraRepo.CreateCamera(ctx, camera)
}

func (s *CameraService) GetCameras(ctx context.Context, getCamerasRequest *dto.GetCamerasRequest) ([]models.Camera, error) {
	return s.cameraRepo.GetCameras(ctx,getCamerasRequest)
}
