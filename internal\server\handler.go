package server

import (
	"net/http"
	controller "scs-camera/internal/controllers"
	repository "scs-camera/internal/repositories"
	service "scs-camera/internal/services"
	ws "scs-camera/internal/websocket"

	myMiddleware "scs-camera/internal/middlewares"

	"github.com/labstack/echo/v4/middleware"

	"github.com/labstack/echo/v4"
)

func (s *Server) MapHandlers(e *echo.Echo) error {
	// Init repositories
	cameraRepo := repository.NewCameraRepository(s.db)
	premiseRepo := repository.NewPremiseRepository(s.db)

	// Init service
	cameraSvc := service.NewCameraService(*cameraRepo, *premiseRepo)

	// Init handlers
	cameraHandler := controller.NewHandler(*cameraSvc)

	// Enable CORS for all origins
	// Enable CORS for all origins
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
		AllowHeaders:     []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
		AllowCredentials: false,
	}))

	mw := myMiddleware.NewMiddlewareManager(s.cfg, []string{"*"}, s.logger)
	e.Use(mw.RequestLoggerMiddleware)
	e.Use(mw.ErrorHandlerMiddleware)
	e.Use(mw.ResponseStandardizer)
	e.GET("/ws", func(c echo.Context) error {
		// Pass both managers to the WebSocket handler
		ws.WSHandler(s.streamManager)(c.Response(), c.Request())
		return nil
	})
	v1 := e.Group("/api/v1")

	health := v1.Group("/health")
	camerasGroup := v1.Group("/cameras", mw.JWTAuth)

	health.GET("", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]string{"status": "OK"})
	})
	cameraHandler.RegisterRoutes(camerasGroup)

	return nil

}
