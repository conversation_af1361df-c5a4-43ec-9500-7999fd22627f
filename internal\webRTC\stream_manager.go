package my_webrtc

import (
	"context"
	"fmt"
	"log"
	"sync"

	"github.com/pion/interceptor"
	"github.com/pion/interceptor/pkg/intervalpli"
	"github.com/pion/webrtc/v4"
)

// CameraActivationCallback is called when a camera publisher starts or stops
type CameraActivationCallback func(cameraID string, isActive bool) error

// StreamManager manages broadcasters per camera/room. Thread-safe.
type StreamManager struct {
	lock                     sync.RWMutex
	broadcasters             map[string]*Broadcaster
	api                      *webrtc.API
	webrtcConfig             webrtc.Configuration
	interceptorReg           *interceptor.Registry
	cameraActivationCallback CameraActivationCallback
}

// NewStreamManager constructs StreamManager with a shared WebRTC API (media codecs & interceptors)
func NewStreamManager(iceServers []webrtc.ICEServer) (*StreamManager, error) {
	mediaEngine := &webrtc.MediaEngine{}
	if err := mediaEngine.RegisterDefaultCodecs(); err != nil {
		return nil, err
	}

	ir := &interceptor.Registry{}
	if err := webrtc.RegisterDefaultInterceptors(mediaEngine, ir); err != nil {
		return nil, err
	}

	pli, err := intervalpli.NewReceiverInterceptor()
	if err != nil {
		return nil, err
	}
	ir.Add(pli)

	api := webrtc.NewAPI(
		webrtc.WithMediaEngine(mediaEngine),
		webrtc.WithInterceptorRegistry(ir),
	)

	cfg := webrtc.Configuration{
		ICEServers: iceServers,
	}

	return &StreamManager{
		broadcasters:   make(map[string]*Broadcaster),
		api:            api,
		webrtcConfig:   cfg,
		interceptorReg: ir,
	}, nil
}

// SetCameraActivationCallback sets the callback function for camera activation/deactivation
func (s *StreamManager) SetCameraActivationCallback(callback CameraActivationCallback) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.cameraActivationCallback = callback
}

// CreateOrGetBroadcaster returns existing or creates new broadcaster for cameraID.
// If created true returned true.
func (s *StreamManager) CreateOrGetBroadcaster(cameraID string) (*Broadcaster, bool) {
	s.lock.Lock()
	defer s.lock.Unlock()
	if b, ok := s.broadcasters[cameraID]; ok {
		return b, false
	}
	b := NewBroadcaster(cameraID, s.api, s.webrtcConfig)
	s.broadcasters[cameraID] = b
	return b, true
}

// RemoveBroadcaster cleans up and deletes broadcaster
func (s *StreamManager) RemoveBroadcaster(cameraID string) {
	s.lock.RLock()
	b, ok := s.broadcasters[cameraID]
	callback := s.cameraActivationCallback
	s.lock.RUnlock()

	if ok && b != nil {
		log.Printf("StreamManager: closing broadcaster %s\n", cameraID)
		b.Close()
		s.lock.Lock()
		delete(s.broadcasters, cameraID)
		s.lock.Unlock()

		// Deactivate the camera when broadcaster is removed
		if callback != nil {
			go func() {
				if deactivationErr := callback(cameraID, false); deactivationErr != nil {
					log.Printf("Failed to deactivate camera %s: %v", cameraID, deactivationErr)
				}
			}()
		}
	}
}

// StartPublisher handles the publisher's offer and returns answer SDP (raw string).
func (s *StreamManager) StartPublisher(ctx context.Context, cameraID string, offer webrtc.SessionDescription, sendCandidate func(sp CandidatePayload) error) (webrtc.SessionDescription, error) {
	b, _ := s.CreateOrGetBroadcaster(cameraID)
	answer, err := b.StartPublisher(ctx, offer, s, sendCandidate)

	// If publisher started successfully, activate the camera
	if err == nil {
		s.lock.RLock()
		callback := s.cameraActivationCallback
		s.lock.RUnlock()

		if callback != nil {
			// Call the activation callback in a goroutine to avoid blocking
			go func() {
				if activationErr := callback(cameraID, true); activationErr != nil {
					log.Printf("Failed to activate camera %s: %v", cameraID, activationErr)
				}
			}()
		}
	}

	return answer, err
}

// AddViewerToBroadcaster creates viewer PC and returns answer
func (s *StreamManager) AddViewerToBroadcaster(ctx context.Context, cameraID, viewerID string, offer webrtc.SessionDescription, sendCandidate func(sp CandidatePayload) error) (webrtc.SessionDescription, error) {
	s.lock.RLock()
	b, ok := s.broadcasters[cameraID]
	s.lock.RUnlock()
	if !ok {
		return webrtc.SessionDescription{}, fmt.Errorf("no broadcaster for camera %s", cameraID)
	}
	return b.AddViewer(ctx, viewerID, offer, sendCandidate)
}

// AddViewerCandidate forwards a candidate to the viewer's PC(s)
func (s *StreamManager) AddViewerCandidate(cameraID, viewerID string, cand webrtc.ICECandidateInit) error {
	s.lock.RLock()
	b, ok := s.broadcasters[cameraID]
	s.lock.RUnlock()
	if !ok {
		return fmt.Errorf("no broadcaster %s", cameraID)
	}
	return b.AddViewerICECandidate(viewerID, cand)
}

// AddPublisherCandidate forwards candidate to publisher PC
func (s *StreamManager) AddPublisherCandidate(cameraID string, cand webrtc.ICECandidateInit) error {
	s.lock.RLock()
	b, ok := s.broadcasters[cameraID]
	s.lock.RUnlock()
	if !ok {
		return fmt.Errorf("no broadcaster %s", cameraID)
	}
	return b.AddPublisherICECandidate(cand)
}
