# Viewer Joining Stream Guide

This guide explains how to implement viewer functionality to join existing camera streams.

## Overview

The system now supports two types of connections:
1. **Publishers** - Camera sources that publish video streams
2. **Viewers** - Clients that want to watch existing streams

## Implementation Methods

### Method 1: WebSocket (Recommended for real-time applications)

#### 1. Connect to WebSocket
```javascript
const ws = new WebSocket('ws://localhost:8080/ws');
```

#### 2. Send Viewer Offer
```javascript
// After getting user media and creating peer connection
const offer = await peerConnection.createOffer();
await peerConnection.setLocalDescription(offer);

const message = {
    type: "viewerOffer",
    cameraId: "target-camera-id",
    payload: {
        viewerId: "unique-viewer-id",
        sdp: btoa(offer.sdp) // Base64 encoded SDP
    }
};

ws.send(JSON.stringify(message));
```

#### 3. Handle Viewer Answer
```javascript
ws.onmessage = async (event) => {
    const message = JSON.parse(event.data);
    
    if (message.type === "viewerAnswer") {
        const answerSdp = atob(message.payload.sdp); // Decode base64
        const answer = {
            type: "answer",
            sdp: answerSdp
        };
        await peerConnection.setRemoteDescription(answer);
    }
    
    if (message.type === "iceCandidate") {
        await peerConnection.addIceCandidate(message.payload);
    }
};
```

#### 4. Handle ICE Candidates
```javascript
peerConnection.onicecandidate = (event) => {
    if (event.candidate) {
        const message = {
            type: "iceCandidate",
            cameraId: "target-camera-id",
            payload: {
                viewerId: "unique-viewer-id", // Optional for viewer candidates
                candidate: event.candidate.candidate,
                sdpMid: event.candidate.sdpMid,
                sdpMLineIndex: event.candidate.sdpMLineIndex
            }
        };
        ws.send(JSON.stringify(message));
    }
};
```

### Method 2: HTTP REST API (Simpler for testing)

#### Join Stream Endpoint
```
POST /api/v1/cameras/join
Content-Type: application/json

{
    "camera_id": "target-camera-id",
    "viewer_id": "unique-viewer-id",
    "sdp": "base64-encoded-sdp-offer"
}
```

#### Response
```json
{
    "sdp": {
        "sdp": "base64-encoded-sdp-answer",
        "type": "answer"
    }
}
```

## Message Types

### WebSocket Message Types

1. **viewerOffer** - Sent by viewer to join a stream
2. **viewerAnswer** - Response from server with SDP answer
3. **iceCandidate** - ICE candidates for connection establishment
4. **error** - Error messages

### Message Structure

```javascript
{
    "type": "viewerOffer|viewerAnswer|iceCandidate|error",
    "cameraId": "camera-identifier",
    "viewerId": "viewer-identifier", // Optional
    "payload": {
        // Type-specific payload
    }
}
```

## Frontend Implementation Example

```html
<!DOCTYPE html>
<html>
<head>
    <title>Stream Viewer</title>
</head>
<body>
    <video id="remoteVideo" autoplay playsinline></video>
    <button onclick="joinStream()">Join Stream</button>

    <script>
        let peerConnection;
        let ws;

        async function joinStream() {
            // Create WebSocket connection
            ws = new WebSocket('ws://localhost:8080/ws');
            
            // Create peer connection
            peerConnection = new RTCPeerConnection({
                iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
            });

            // Handle remote stream
            peerConnection.ontrack = (event) => {
                document.getElementById('remoteVideo').srcObject = event.streams[0];
            };

            // Handle ICE candidates
            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    ws.send(JSON.stringify({
                        type: "iceCandidate",
                        cameraId: "your-camera-id",
                        payload: {
                            viewerId: "your-viewer-id",
                            candidate: event.candidate.candidate,
                            sdpMid: event.candidate.sdpMid,
                            sdpMLineIndex: event.candidate.sdpMLineIndex
                        }
                    }));
                }
            };

            // Create offer and send to server
            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);

            ws.onopen = () => {
                ws.send(JSON.stringify({
                    type: "viewerOffer",
                    cameraId: "your-camera-id",
                    payload: {
                        viewerId: "your-viewer-id",
                        sdp: btoa(offer.sdp)
                    }
                }));
            };

            // Handle server responses
            ws.onmessage = async (event) => {
                const message = JSON.parse(event.data);
                
                if (message.type === "viewerAnswer") {
                    const answer = {
                        type: "answer",
                        sdp: atob(message.payload.sdp)
                    };
                    await peerConnection.setRemoteDescription(answer);
                }
                
                if (message.type === "iceCandidate") {
                    await peerConnection.addIceCandidate(message.payload);
                }
                
                if (message.type === "error") {
                    console.error("Server error:", message.message);
                }
            };
        }
    </script>
</body>
</html>
```

## Testing

1. Start a camera stream (publisher)
2. Use the viewer implementation to join the stream
3. Check browser console for any WebRTC errors
4. Monitor server logs for connection status

## Error Handling

Common errors and solutions:

- **"Broadcaster not found"** - Ensure the camera stream is active
- **"Stream not ready"** - Wait for the publisher to establish connection
- **ICE connection failures** - Check STUN/TURN server configuration
- **SDP parsing errors** - Verify base64 encoding/decoding

## Next Steps

- Implement viewer disconnection handling
- Add viewer count tracking
- Implement viewer-specific ICE candidate handling
- Add authentication for viewer access
