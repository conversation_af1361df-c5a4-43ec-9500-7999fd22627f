package my_webrtc

import (
	"encoding/json"
)

// SignalingMessage is the clean message schema used over WebSocket
type SignalingMessage struct {
	Type      string            `json:"type"`                // "role", "offer", "answer", "candidate", "leave", "error"
	Role      string            `json:"role,omitempty"`      // "publisher" | "viewer"
	CameraID  string            `json:"cameraId,omitempty"`  // camera / room identifier
	ViewerID  string            `json:"viewerId,omitempty"`  // viewer or publisher id
	SDP       *SDPPayload       `json:"sdp,omitempty"`       // for offer/answer
	Candidate *CandidatePayload `json:"candidate,omitempty"` // for ICE candidate
}

// SDPPayload contains sdp + type (offer/answer)
type SDPPayload struct {
	Type string `json:"type"`
	SDP  string `json:"sdp"` // base64 or raw depending on client; we assume raw SDP string
}

// CandidatePayload is the ICE candidate info
type CandidatePayload struct {
	Candidate     string  `json:"candidate"`
	SDPMid        *string `json:"sdpMid,omitempty"`
	SDPMLineIndex *uint16 `json:"sdpMLineIndex,omitempty"`
}

// Marshal helper
func (m *SignalingMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}
