package repositories

import (
	"context"
	"fmt"
	"scs-camera/internal/dto"
	"scs-camera/internal/models"

	"gorm.io/gorm"
)

type CameraRepository struct {
	db *gorm.DB
}

func NewCameraRepository(db *gorm.DB) *CameraRepository {
	return &CameraRepository{db: db}
}

func (r *CameraRepository) CreateCamera(ctx context.Context, camera *models.Camera) (*models.Camera, error) {
	if err := r.db.WithContext(ctx).Create(camera).Error; err != nil {
		return nil, fmt.Errorf("failed to create Camera: %w", err)
	}
	return camera, nil
}
func (r *CameraRepository) GetCameras(ctx context.Context, getCamerasRequest *dto.GetCamerasRequest) ([]models.Camera, error) {
	var Cameras []models.Camera
	queryBuilder := r.db.WithContext(ctx).Preload("Premise")
	if getCamerasRequest.PremiseID != "" {
		queryBuilder = queryBuilder.Where("premise_id = ?", getCamerasRequest.PremiseID)
	}
	if err := queryBuilder.Find(&Cameras).Error; err != nil {
		return nil, fmt.Errorf("failed to get Cameras: %w", err)
	}
	return Cameras, nil
}

func (r *CameraRepository) GetCameraByID(ctx context.Context, id string) (*models.Camera, error) {
	var Camera models.Camera
	if err := r.db.WithContext(ctx).First(&Camera, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to get Camera: %w", err)
	}
	return &Camera, nil
}
