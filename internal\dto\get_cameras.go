package dto

type GetCamerasResponse struct {
	ID                  string `json:"id"`
	Name                string `json:"name"`
	LocationDescription string `json:"location_description"`
	PremiseID           string `json:"premise_id"`
	StreamURL           string `json:"stream_url"`
	IsActive            bool   `json:"is_active"`
}

type GetCamerasRequest struct {
	PremiseID string `json:"premise_id"`
	Page      int    `json:"page"`
	Limit     int    `json:"limit"`
}
