package main

import (
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"time"
	ws "scs-camera/internal/websocket"

	"github.com/gorilla/websocket"
)

// Test to verify the WebSocket concurrency fix
func main() {
	fmt.Println("=== WebSocket Concurrency Fix Test ===\n")

	// Test 1: SafeWebSocketConn concurrent writes
	fmt.Println("1. Testing SafeWebSocketConn concurrent writes...")
	testSafeWebSocketConn()

	// Test 2: ConnectionManager concurrent operations
	fmt.Println("\n2. Testing ConnectionManager concurrent operations...")
	testConnectionManager()

	// Test 3: Stress test with many concurrent operations
	fmt.Println("\n3. Running stress test...")
	stressTest()

	fmt.Println("\n=== All tests completed successfully! ===")
}

// Test SafeWebSocketConn with concurrent writes
func testSafeWebSocketConn() {
	// Create a mock WebSocket server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		upgrader := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true },
		}
		
		conn, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			log.Printf("Upgrade error: %v", err)
			return
		}
		defer conn.Close()

		// Read messages and echo them back
		for {
			var message map[string]interface{}
			if err := conn.ReadJSON(&message); err != nil {
				break
			}
			// Echo back with confirmation
			response := map[string]interface{}{
				"type": "echo",
				"original": message,
			}
			conn.WriteJSON(response)
		}
	}))
	defer server.Close()

	// Connect to the test server
	url := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("Dial error:", err)
	}
	defer conn.Close()

	// Create safe wrapper
	safeConn := ws.NewSafeWebSocketConn(conn)

	// Test concurrent writes
	const numGoroutines = 50
	const messagesPerGoroutine = 10
	var wg sync.WaitGroup
	
	fmt.Printf("   Starting %d goroutines, each sending %d messages...\n", numGoroutines, messagesPerGoroutine)
	
	start := time.Now()
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			for j := 0; j < messagesPerGoroutine; j++ {
				message := map[string]interface{}{
					"goroutine": goroutineID,
					"message":   j,
					"timestamp": time.Now().UnixNano(),
				}
				
				if err := safeConn.WriteJSON(message); err != nil {
					log.Printf("Write error in goroutine %d: %v", goroutineID, err)
					return
				}
			}
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	totalMessages := numGoroutines * messagesPerGoroutine
	fmt.Printf("   ✅ Successfully sent %d messages in %v (%.2f msg/sec)\n", 
		totalMessages, duration, float64(totalMessages)/duration.Seconds())
}

// Test ConnectionManager with concurrent operations
func testConnectionManager() {
	cm := ws.GetConnectionManager()
	
	// Create mock connections
	const numConnections = 20
	mockConnections := make([]*websocket.Conn, numConnections)
	
	fmt.Printf("   Creating %d mock connections...\n", numConnections)
	
	for i := 0; i < numConnections; i++ {
		// Create a mock connection (in real scenario, these would be actual WebSocket connections)
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			upgrader := websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}
			conn, _ := upgrader.Upgrade(w, r, nil)
			defer conn.Close()
			
			// Keep connection alive
			for {
				if _, _, err := conn.ReadMessage(); err != nil {
					break
				}
			}
		}))
		defer server.Close()
		
		url := "ws" + strings.TrimPrefix(server.URL, "http")
		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			log.Printf("Failed to create mock connection %d: %v", i, err)
			continue
		}
		
		mockConnections[i] = conn
		connectionID := fmt.Sprintf("test_conn_%d", i)
		cm.AddConnection(connectionID, conn)
	}
	
	fmt.Printf("   Active connections: %d\n", cm.GetActiveConnections())
	
	// Test concurrent broadcasting
	const numBroadcasts = 100
	var wg sync.WaitGroup
	
	fmt.Printf("   Testing %d concurrent broadcasts...\n", numBroadcasts)
	
	start := time.Now()
	for i := 0; i < numBroadcasts; i++ {
		wg.Add(1)
		go func(broadcastID int) {
			defer wg.Done()
			message := map[string]interface{}{
				"type":        "broadcast",
				"broadcastId": broadcastID,
				"timestamp":   time.Now().UnixNano(),
				"data":        fmt.Sprintf("Broadcast message %d", broadcastID),
			}
			cm.BroadcastToAll(message)
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	fmt.Printf("   ✅ Successfully completed %d broadcasts in %v\n", numBroadcasts, duration)
	
	// Clean up connections
	for i, conn := range mockConnections {
		if conn != nil {
			conn.Close()
			cm.RemoveConnection(fmt.Sprintf("test_conn_%d", i))
		}
	}
	
	fmt.Printf("   Active connections after cleanup: %d\n", cm.GetActiveConnections())
}

// Stress test with high concurrency
func stressTest() {
	const (
		numConnections = 100
		numGoroutines = 200
		operationsPerGoroutine = 50
	)
	
	fmt.Printf("   Stress test: %d connections, %d goroutines, %d ops each\n", 
		numConnections, numGoroutines, operationsPerGoroutine)
	
	cm := ws.GetConnectionManager()
	
	// Create connections
	connections := make([]*websocket.Conn, numConnections)
	for i := 0; i < numConnections; i++ {
		// For stress test, we'll use a simpler mock
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			upgrader := websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}
			conn, _ := upgrader.Upgrade(w, r, nil)
			defer conn.Close()
			
			// Simple echo server
			for {
				if _, _, err := conn.ReadMessage(); err != nil {
					break
				}
			}
		}))
		defer server.Close()
		
		url := "ws" + strings.TrimPrefix(server.URL, "http")
		conn, _, err := websocket.DefaultDialer.Dial(url, nil)
		if err != nil {
			continue
		}
		
		connections[i] = conn
		cm.AddConnection(fmt.Sprintf("stress_conn_%d", i), conn)
	}
	
	fmt.Printf("   Created %d connections\n", cm.GetActiveConnections())
	
	// Run stress test
	var wg sync.WaitGroup
	start := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			
			for j := 0; j < operationsPerGoroutine; j++ {
				// Mix of operations
				switch j % 3 {
				case 0:
					// Broadcast
					cm.BroadcastToAll(map[string]interface{}{
						"type": "stress_broadcast",
						"id":   fmt.Sprintf("%d_%d", goroutineID, j),
					})
				case 1:
					// Get connection
					connID := fmt.Sprintf("stress_conn_%d", j%numConnections)
					if conn, exists := cm.GetConnection(connID); exists && !conn.IsClosed() {
						conn.WriteJSON(map[string]interface{}{
							"type": "stress_message",
							"id":   fmt.Sprintf("%d_%d", goroutineID, j),
						})
					}
				case 2:
					// Check active connections
					_ = cm.GetActiveConnections()
				}
			}
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	totalOperations := numGoroutines * operationsPerGoroutine
	fmt.Printf("   ✅ Completed %d operations in %v (%.2f ops/sec)\n", 
		totalOperations, duration, float64(totalOperations)/duration.Seconds())
	
	// Cleanup
	for i, conn := range connections {
		if conn != nil {
			conn.Close()
			cm.RemoveConnection(fmt.Sprintf("stress_conn_%d", i))
		}
	}
	
	fmt.Printf("   Final active connections: %d\n", cm.GetActiveConnections())
}

// Helper function to simulate network delay
func simulateNetworkDelay() {
	time.Sleep(time.Millisecond * time.Duration(1+time.Now().UnixNano()%5))
}
