package http

import (
	dto "scs-camera/internal/dto"
	services "scs-camera/internal/services"

	"github.com/labstack/echo/v4"
)

// Handler
type CameraHandler struct {
	svc services.CameraService
}

// NewHandler constructor
func NewHandler(svc services.CameraService) *CameraHandler {
	return &CameraHandler{svc: svc}
}

func (h *CameraHandler) CreateCamera() echo.HandlerFunc {
	return func(c echo.Context) error {
		createCameraDto := &dto.CreateCameraDto{}
		if err := c.Bind(createCameraDto); err != nil {
			return err
		}
		createdCamera, err := h.svc.CreateCamera(c.Request().Context(), createCameraDto)
		if err != nil {
			return err
		}
		return c.JSON(201, createdCamera)
	}
}

func (h *CameraHandler) GetCameras() echo.HandlerFunc {
	return func(c echo.Context) error {
		getCamerasRequest := &dto.GetCamerasRequest{}
		premiseId := c.QueryParam("premise_id")
		if premiseId != "" {
			getCamerasRequest.PremiseID = premiseId
		}

		Cameras, err := h.svc.GetCameras(c.Request().Context(), getCamerasRequest)
		if err != nil {
			return err
		}
		return c.JSON(200, Cameras)
	}
}
