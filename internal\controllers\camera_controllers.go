package http

import (
	dto "scs-camera/internal/dto"
	services "scs-camera/internal/services"
	"scs-camera/pkg/errors"
	"strconv"

	"github.com/labstack/echo/v4"
)

// Handler
type CameraHandler struct {
	svc services.CameraService
}

// NewHandler constructor
func NewHandler(svc services.CameraService) *CameraHandler {
	return &CameraHandler{svc: svc}
}

func (h *CameraHandler) CreateCamera() echo.HandlerFunc {
	return func(c echo.Context) error {
		createCameraDto := &dto.CreateCameraDto{}
		if err := c.Bind(createCameraDto); err != nil {
			return err
		}
		createdCamera, err := h.svc.CreateCamera(c.Request().Context(), createCameraDto)
		if err != nil {
			return err
		}
		return c.JSON(201, createdCamera)
	}
}

func (h *CameraHandler) GetCameras() echo.HandlerFunc {
	return func(c echo.Context) error {
		getCamerasRequest := &dto.GetCamerasRequest{}
		premiseId := c.QueryParam("premise_id")
		if premiseId != "" {
			getCamerasRequest.PremiseID = premiseId
		}
		page := c.QueryParam("page")
		limit := c.QueryParam("limit")
		if page == "" {
			page = "1"
		}
		if limit == "" {
			limit = "4"
		}
		pageInt, err := strconv.Atoi(page)
		if err != nil {
			return errors.NewBadRequestError("Invalid page number")
		}
		getCamerasRequest.Page = pageInt
		limitInt, err := strconv.Atoi(limit)
		if err != nil {
			return errors.NewBadRequestError("Invalid limit number")
		}
		getCamerasRequest.Limit = limitInt

		Cameras, err := h.svc.GetCameras(c.Request().Context(), getCamerasRequest)
		if err != nil {
			return err
		}
		return c.JSON(200, Cameras)
	}
}
func (h *CameraHandler) UpdateCamera() echo.HandlerFunc {
	return func(c echo.Context) error {
		cameraId := c.Param("id")
		updateCameraDto := &dto.UpdateCameraDto{}
		if err := c.Bind(updateCameraDto); err != nil {
			return err
		}
		updatedCamera, err := h.svc.UpdateCamera(c.Request().Context(), cameraId, updateCameraDto)
		if err != nil {
			return err
		}
		return c.JSON(200, updatedCamera)
	}
}
