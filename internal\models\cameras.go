package models

import "github.com/google/uuid"

type Camera struct {
	Base
	Name                string    `json:"name"`
	LocationDescription string    `json:"location_description"`
	PremiseID           uuid.UUID `json:"premise_id"`
	Premise             *Premise  ` json:"premise,omitempty" gorm:"foreignKey:PremiseID"`
	StreamURL           string    `json:"stream_url"`
	IsActive            bool      `json:"is_active"`
	
}
