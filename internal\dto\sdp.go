package dto

import (
	"encoding/json"
	my_webrtc "scs-camera/internal/webRTC"
)

type AddSubscriber struct {
	SDP      Sdp    `json:"sdp"`
	ViewerID string `json:"viewer_id"`
}

type AddSubscriberResponse struct {
	CameraID   string                       `json:"camera_id"`
	SDP        Sdp                          `json:"sdp"`
	ViewerID   string                       `json:"viewer_id"`
	Candidates []my_webrtc.CandidatePayload `json:"candidates"`
}

type Sdp struct {
	SDP  string `json:"sdp"`
	Type string `json:"type"`
}

type SignalingMessage struct {
	Type     string          `json:"type"`               // "sdpOffer", "sdpAnswer", "iceCandidate", "viewerOffer", "viewerAnswer"
	Payload  json.RawMessage `json:"payload"`            // Raw JSON for SDP or ICE candidate
	CameraID string          `json:"cameraId"`           // Which camera stream this message belongs to
	ViewerID string          `json:"viewerId,omitempty"` // Optional viewer ID for viewer-specific messages
}
