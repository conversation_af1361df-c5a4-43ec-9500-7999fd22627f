<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stream Viewer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        input, button {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .logs {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Stream Viewer Test</h1>
        
        <div class="controls">
            <label for="cameraId">Camera ID:</label>
            <input type="text" id="cameraId" placeholder="Enter camera ID" value="test-camera-1">
            
            <label for="viewerId">Viewer ID:</label>
            <input type="text" id="viewerId" placeholder="Enter viewer ID" value="viewer-1">
            
            <button id="joinBtn" onclick="joinStream()">Join Stream</button>
            <button id="leaveBtn" onclick="leaveStream()" disabled>Leave Stream</button>
        </div>

        <div id="status" class="status info">Ready to join stream</div>

        <video id="remoteVideo" autoplay playsinline muted>
            <p>Your browser doesn't support video playback.</p>
        </video>

        <div>
            <h3>Connection Logs</h3>
            <div id="logs" class="logs"></div>
        </div>
    </div>

    <script>
        let peerConnection = null;
        let ws = null;
        let isConnected = false;

        const statusDiv = document.getElementById('status');
        const logsDiv = document.getElementById('logs');
        const joinBtn = document.getElementById('joinBtn');
        const leaveBtn = document.getElementById('leaveBtn');
        const remoteVideo = document.getElementById('remoteVideo');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            console.log(logEntry);
            logsDiv.innerHTML += logEntry + '\n';
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            log(`Status: ${message}`);
        }

        async function joinStream() {
            const cameraId = document.getElementById('cameraId').value.trim();
            const viewerId = document.getElementById('viewerId').value.trim();

            if (!cameraId || !viewerId) {
                updateStatus('Please enter both Camera ID and Viewer ID', 'error');
                return;
            }

            try {
                updateStatus('Connecting to stream...', 'info');
                joinBtn.disabled = true;

                // Create WebSocket connection
                ws = new WebSocket('ws://localhost:1325/ws');
                
                ws.onopen = async () => {
                    log('WebSocket connected');
                    await setupPeerConnection(cameraId, viewerId);
                };

                ws.onmessage = async (event) => {
                    const message = JSON.parse(event.data);
                    log(`Received: ${message.type}`);

                    if (message.type === "answer") {
                        log('Processing answer...');
                        const answer = {
                            type: "answer",
                            sdp: message.sdp.sdp
                        };
                        await peerConnection.setRemoteDescription(answer);
                        updateStatus('Connected to stream!', 'success');
                        isConnected = true;
                        leaveBtn.disabled = false;
                    }

                    if (message.type === "candidate") {
                        log('Adding ICE candidate...');
                        const candidate = {
                            candidate: message.candidate.candidate,
                            sdpMid: message.candidate.sdpMid,
                            sdpMLineIndex: message.candidate.sdpMLineIndex
                        };
                        await peerConnection.addIceCandidate(candidate);
                    }

                    if (message.type === "error") {
                        updateStatus(`Error: Server error`, 'error');
                        log(`Server error received`);
                        cleanup();
                    }
                };

                ws.onerror = (error) => {
                    log(`WebSocket error: ${error}`);
                    updateStatus('WebSocket connection failed', 'error');
                    cleanup();
                };

                ws.onclose = () => {
                    log('WebSocket disconnected');
                    if (isConnected) {
                        updateStatus('Connection lost', 'error');
                    }
                    cleanup();
                };

            } catch (error) {
                log(`Error joining stream: ${error.message}`);
                updateStatus(`Failed to join stream: ${error.message}`, 'error');
                cleanup();
            }
        }

        async function setupPeerConnection(cameraId, viewerId) {
            // Create peer connection
            peerConnection = new RTCPeerConnection({
                iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
            });

            // Add receive-only transceiver for video BEFORE creating offer
            peerConnection.addTransceiver('video', { direction: 'recvonly' });

            // Handle remote stream
            peerConnection.ontrack = (event) => {
                log('Received remote track');
                log(`Track kind: ${event.track.kind}, readyState: ${event.track.readyState}`);

                // Create a new MediaStream and add the track
                const stream = new MediaStream([event.track]);
                remoteVideo.srcObject = stream;

                // Ensure video plays
                remoteVideo.play().catch(err => {
                    log(`Video play failed: ${err.message}`);
                });
            };

            // Handle ICE candidates
            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    log('Sending ICE candidate...');
                    ws.send(JSON.stringify({
                        type: "candidate",
                        role: "viewer",
                        cameraID: cameraId,
                        viewerID: viewerId,
                        candidate: {
                            candidate: event.candidate.candidate,
                            sdpMid: event.candidate.sdpMid,
                            sdpMLineIndex: event.candidate.sdpMLineIndex
                        }
                    }));
                }
            };

            // Handle connection state changes
            peerConnection.onconnectionstatechange = () => {
                log(`Connection state: ${peerConnection.connectionState}`);
                if (peerConnection.connectionState === 'failed') {
                    updateStatus('Connection failed', 'error');
                    cleanup();
                }
            };

            // Create offer and send to server
            log('Creating offer...');
            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);

            // Debug: Log the SDP content
            log(`SDP Offer created, length: ${offer.sdp.length}`);
            log(`SDP contains ice-ufrag: ${offer.sdp.includes('ice-ufrag')}`);
            log(`SDP contains ice-pwd: ${offer.sdp.includes('ice-pwd')}`);

            // Show first 200 characters of SDP for debugging
            log(`SDP preview: ${offer.sdp.substring(0, 200)}...`);

            // Send role message first
            log('Sending role message...');
            ws.send(JSON.stringify({
                type: "role",
                role: "viewer",
                viewerID: viewerId
            }));

            // Send offer with correct format
            log('Sending viewer offer...');
            const message = {
                type: "offer",
                role: "viewer",
                cameraID: cameraId,
                viewerID: viewerId,
                sdp: {
                    type: "offer",
                    sdp: offer.sdp
                }
            };

            log(`Message payload: ${JSON.stringify(message, null, 2)}`);
            ws.send(JSON.stringify(message));
        }

        function leaveStream() {
            log('Leaving stream...');
            cleanup();
            updateStatus('Disconnected from stream', 'info');
        }

        function cleanup() {
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            
            if (ws) {
                ws.close();
                ws = null;
            }

            if (remoteVideo.srcObject) {
                remoteVideo.srcObject = null;
            }

            isConnected = false;
            joinBtn.disabled = false;
            leaveBtn.disabled = true;
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', cleanup);
    </script>
</body>
</html>
