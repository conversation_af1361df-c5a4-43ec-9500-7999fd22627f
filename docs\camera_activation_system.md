# Camera Activation System

This document explains the automatic camera activation system that activates cameras when publishers start successfully and deactivates them when publishers stop.

## Overview

The camera activation system automatically manages camera status based on the WebRTC publisher lifecycle:

- **Camera Activation**: When a publisher successfully starts streaming, the camera is marked as active
- **Camera Deactivation**: When a publisher stops or disconnects, the camera is marked as inactive

## Architecture

### Components

1. **CameraService**: Manages camera status in the database
2. **StreamManager**: Handles WebRTC publishers and triggers activation callbacks
3. **CameraRepository**: Provides database operations for camera status updates
4. **Activation Callback**: Links the streaming system to camera management

### Flow Diagram

```
Publisher Start → StreamManager.StartPublisher() → Success? → Activation Callback → CameraService.ActivateCamera() → Database Update
Publisher Stop  → StreamManager.RemoveBroadcaster() → Deactivation Callback → CameraService.DeactivateCamera() → Database Update
```

## Implementation Details

### 1. Camera Repository Methods

```go
// UpdateCameraStatus updates the IsActive status of a camera
func (r *CameraRepository) UpdateCameraStatus(ctx context.Context, cameraID string, isActive bool) error

// UpdateCamera updates a camera record
func (r *CameraRepository) UpdateCamera(ctx context.Context, camera *models.Camera) (*models.Camera, error)
```

### 2. Camera Service Methods

```go
// ActivateCamera sets a camera as active when publisher starts successfully
func (s *CameraService) ActivateCamera(ctx context.Context, cameraID string) error

// DeactivateCamera sets a camera as inactive when publisher stops
func (s *CameraService) DeactivateCamera(ctx context.Context, cameraID string) error
```

### 3. Stream Manager Integration

```go
// CameraActivationCallback is called when a camera publisher starts or stops
type CameraActivationCallback func(cameraID string, isActive bool) error

// SetCameraActivationCallback sets the callback function for camera activation/deactivation
func (s *StreamManager) SetCameraActivationCallback(callback CameraActivationCallback)
```

### 4. Server Setup

In the server initialization, the callback is configured:

```go
// Set up camera activation callback for stream manager
s.streamManager.SetCameraActivationCallback(func(cameraID string, isActive bool) error {
    ctx := context.Background()
    if isActive {
        return cameraSvc.ActivateCamera(ctx, cameraID)
    } else {
        return cameraSvc.DeactivateCamera(ctx, cameraID)
    }
})
```

## Usage Examples

### Automatic Activation

When a client starts publishing:

```javascript
// Client-side: Start publishing
const offer = await peerConnection.createOffer();
await peerConnection.setLocalDescription(offer);

ws.send(JSON.stringify({
    type: "offer",
    role: "publisher",
    cameraId: "camera_001",
    sdp: {
        type: "offer",
        sdp: offer.sdp
    }
}));

// Server automatically activates camera_001 when publisher starts successfully
```

### Manual Control

You can also manually control camera status:

```go
// Manual activation
err := cameraService.ActivateCamera(ctx, "camera_001")

// Manual deactivation  
err := cameraService.DeactivateCamera(ctx, "camera_001")
```

### Checking Camera Status

```go
camera, err := cameraService.GetCameraByID(ctx, "camera_001")
if err != nil {
    return err
}

if camera.IsActive {
    fmt.Println("Camera is currently streaming")
} else {
    fmt.Println("Camera is offline")
}
```

## Database Schema

The camera model includes an `IsActive` field:

```go
type Camera struct {
    Base
    Name                string    `json:"name"`
    LocationDescription string    `json:"location_description"`
    PremiseID           uuid.UUID `json:"premise_id"`
    Premise             *Premise  `json:"premise,omitempty" gorm:"foreignKey:PremiseID"`
    StreamURL           string    `json:"stream_url"`
    IsActive            bool      `json:"is_active"`  // Activation status
}
```

## Error Handling

### Activation Failures

- If camera activation fails, the error is logged but doesn't affect the publisher
- The publisher continues to work even if database update fails
- Callbacks run in goroutines to avoid blocking the streaming process

### Deactivation Failures

- Similar to activation, deactivation failures are logged but don't affect cleanup
- Broadcaster removal continues even if camera deactivation fails

## Benefits

1. **Automatic Status Management**: No manual intervention required
2. **Real-time Updates**: Camera status reflects actual streaming state
3. **Reliable**: Callbacks are non-blocking and error-tolerant
4. **Scalable**: Works with multiple cameras and publishers
5. **Consistent**: Database always reflects current streaming status

## Monitoring and Debugging

### Logs

The system provides detailed logging:

```
StreamManager: closing broadcaster camera_001
📴 Deactivating camera: camera_001
📹 Activating camera: camera_001
Failed to activate camera camera_001: database connection error
```

### Database Queries

Check camera status directly:

```sql
SELECT id, name, is_active FROM cameras WHERE id = 'camera_001';
```

### API Endpoints

Get camera status via API:

```bash
GET /api/v1/cameras/camera_001
```

Response:
```json
{
    "id": "camera_001",
    "name": "Front Door Camera",
    "is_active": true,
    "location_description": "Main entrance"
}
```

## Testing

The system includes comprehensive tests:

- Unit tests for camera service methods
- Integration tests for the full activation flow
- Mock tests for error scenarios
- Performance tests for high-load scenarios

See `examples/camera_activation_test.go` for a complete test example.

## Future Enhancements

Potential improvements:

1. **WebSocket Notifications**: Broadcast camera status changes to connected clients
2. **Health Checks**: Periodic verification of camera status
3. **Metrics**: Track activation/deactivation rates and failures
4. **Batch Operations**: Handle multiple camera status updates efficiently
5. **Audit Trail**: Log all camera status changes for compliance
